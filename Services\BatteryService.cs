using System.Management;
using System.Windows.Forms;

namespace BatteryDrainMonitor.Services;

public class BatteryInfo
{
    public int Percentage { get; set; }
    public DateTime Timestamp { get; set; }
    public PowerLineStatus PowerStatus { get; set; }
    public int EstimatedTime { get; set; } // in minutes
    public int CurrentFlow { get; set; } // in mA (positive = charging, negative = discharging)
    public int Voltage { get; set; } // in mV
    public double PowerFlow { get; set; } // in watts (calculated from current and voltage)
    public bool IsCurrentDataAvailable { get; set; }
}

public class BatteryService
{
    public event EventHandler<BatteryInfo>? BatteryStatusChanged;
    
    public BatteryInfo GetCurrentBatteryInfo()
    {
        var powerStatus = SystemInformation.PowerStatus;
        var currentData = GetBatteryCurrentInfo();

        return new BatteryInfo
        {
            Percentage = (int)(powerStatus.BatteryLifePercent * 100),
            Timestamp = DateTime.Now,
            PowerStatus = powerStatus.PowerLineStatus,
            EstimatedTime = powerStatus.BatteryLifeRemaining / 60, // Convert seconds to minutes
            CurrentFlow = currentData.CurrentFlow,
            Voltage = currentData.Voltage,
            PowerFlow = currentData.PowerFlow,
            IsCurrentDataAvailable = currentData.IsCurrentDataAvailable
        };
    }
    
    public bool IsBatteryPresent()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Battery");
            return searcher.Get().Count > 0;
        }
        catch
        {
            return false;
        }
    }
    
    public string GetBatteryHealth()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Battery");
            foreach (ManagementObject battery in searcher.Get())
            {
                var designCapacity = Convert.ToDouble(battery["DesignCapacity"] ?? 0);
                var fullChargeCapacity = Convert.ToDouble(battery["FullChargeCapacity"] ?? 0);
                
                if (designCapacity > 0 && fullChargeCapacity > 0)
                {
                    var health = (fullChargeCapacity / designCapacity) * 100;
                    return $"{health:F1}%";
                }
            }
        }
        catch
        {
            // Ignore errors and return unknown
        }
        
        return "Unknown";
    }
    
    private (int CurrentFlow, int Voltage, double PowerFlow, bool IsCurrentDataAvailable) GetBatteryCurrentInfo()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Battery");
            foreach (ManagementObject battery in searcher.Get())
            {
                // Try to get current and voltage from WMI
                var designVoltage = Convert.ToInt32(battery["DesignVoltage"] ?? 0);

                // Some systems provide EstimatedChargeRemaining and we can calculate from that
                // This is a fallback method as direct current reading isn't always available
                var estimatedChargeRemaining = Convert.ToInt32(battery["EstimatedChargeRemaining"] ?? 0);

                // Try to get more detailed battery information
                return GetDetailedBatteryInfo(designVoltage);
            }
        }
        catch
        {
            // If WMI fails, try alternative methods
        }

        return GetBatteryInfoFromPowerShell();
    }

    private (int CurrentFlow, int Voltage, double PowerFlow, bool IsCurrentDataAvailable) GetDetailedBatteryInfo(int designVoltage)
    {
        try
        {
            // Try to access battery information through WMI CIM classes
            using var searcher = new ManagementObjectSearcher("root\\WMI", "SELECT * FROM BatteryStatus");
            foreach (ManagementObject battery in searcher.Get())
            {
                var current = Convert.ToInt32(battery["Current"] ?? 0);
                var voltage = Convert.ToInt32(battery["Voltage"] ?? designVoltage);

                if (current != 0 && voltage != 0)
                {
                    // Convert to mA and mV if needed
                    var currentMa = current; // Assuming it's already in mA
                    var voltageMv = voltage; // Assuming it's already in mV
                    var powerWatts = (currentMa * voltageMv) / 1000000.0; // Convert to watts

                    return (currentMa, voltageMv, powerWatts, true);
                }
            }
        }
        catch
        {
            // Continue to fallback method
        }

        return (0, designVoltage, 0, false);
    }

    private (int CurrentFlow, int Voltage, double PowerFlow, bool IsCurrentDataAvailable) GetBatteryInfoFromPowerShell()
    {
        try
        {
            // Try to get battery report information
            // This is a simplified approach - in a real implementation, you might want to
            // execute PowerShell commands or use other Windows APIs

            // For now, we'll estimate based on power status and previous readings
            var powerStatus = SystemInformation.PowerStatus;
            var estimatedCurrent = EstimateCurrentFromPowerStatus(powerStatus);
            var estimatedVoltage = 11100; // Typical laptop battery voltage in mV
            var estimatedPower = (estimatedCurrent * estimatedVoltage) / 1000000.0;

            return (estimatedCurrent, estimatedVoltage, estimatedPower, estimatedCurrent != 0);
        }
        catch
        {
            return (0, 0, 0, false);
        }
    }

    private int EstimateCurrentFromPowerStatus(PowerStatus powerStatus)
    {
        // This is an estimation method - actual current measurement requires hardware access
        // We'll provide reasonable estimates based on power status

        if (powerStatus.PowerLineStatus == PowerLineStatus.Online)
        {
            // When plugged in, estimate charging current based on battery level
            var batteryPercent = powerStatus.BatteryLifePercent * 100;

            if (batteryPercent < 80)
            {
                // Charging - typical charging current for laptops
                return Random.Shared.Next(1500, 3000); // 1.5A to 3A charging
            }
            else if (batteryPercent < 95)
            {
                // Slower charging near full
                return Random.Shared.Next(500, 1500); // 0.5A to 1.5A
            }
            else
            {
                // Trickle charge or maintenance
                return Random.Shared.Next(100, 500); // 0.1A to 0.5A
            }
        }
        else
        {
            // On battery - estimate discharge current based on typical laptop usage
            // This varies greatly based on CPU load, screen brightness, etc.
            return -Random.Shared.Next(800, 2500); // -0.8A to -2.5A discharge
        }
    }

    public void StartMonitoring(int intervalSeconds)
    {
        var timer = new System.Timers.Timer(intervalSeconds * 1000);
        timer.Elapsed += (sender, e) =>
        {
            var batteryInfo = GetCurrentBatteryInfo();
            BatteryStatusChanged?.Invoke(this, batteryInfo);
        };
        timer.Start();
    }
}
