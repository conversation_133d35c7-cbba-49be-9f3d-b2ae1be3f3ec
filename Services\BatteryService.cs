using System.Management;
using System.Windows.Forms;

namespace BatteryDrainMonitor.Services;

public class BatteryInfo
{
    public int Percentage { get; set; }
    public DateTime Timestamp { get; set; }
    public PowerLineStatus PowerStatus { get; set; }
    public int EstimatedTime { get; set; } // in minutes
}

public class BatteryService
{
    public event EventHandler<BatteryInfo>? BatteryStatusChanged;
    
    public BatteryInfo GetCurrentBatteryInfo()
    {
        var powerStatus = SystemInformation.PowerStatus;
        
        return new BatteryInfo
        {
            Percentage = (int)(powerStatus.BatteryLifePercent * 100),
            Timestamp = DateTime.Now,
            PowerStatus = powerStatus.PowerLineStatus,
            EstimatedTime = powerStatus.BatteryLifeRemaining / 60 // Convert seconds to minutes
        };
    }
    
    public bool IsBatteryPresent()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Battery");
            return searcher.Get().Count > 0;
        }
        catch
        {
            return false;
        }
    }
    
    public string GetBatteryHealth()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Battery");
            foreach (ManagementObject battery in searcher.Get())
            {
                var designCapacity = Convert.ToDouble(battery["DesignCapacity"] ?? 0);
                var fullChargeCapacity = Convert.ToDouble(battery["FullChargeCapacity"] ?? 0);
                
                if (designCapacity > 0 && fullChargeCapacity > 0)
                {
                    var health = (fullChargeCapacity / designCapacity) * 100;
                    return $"{health:F1}%";
                }
            }
        }
        catch
        {
            // Ignore errors and return unknown
        }
        
        return "Unknown";
    }
    
    public void StartMonitoring(int intervalSeconds)
    {
        var timer = new System.Timers.Timer(intervalSeconds * 1000);
        timer.Elapsed += (sender, e) =>
        {
            var batteryInfo = GetCurrentBatteryInfo();
            BatteryStatusChanged?.Invoke(this, batteryInfo);
        };
        timer.Start();
    }
}
