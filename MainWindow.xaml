<Window x:Class="BatteryDrainMonitor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Battery Drain Monitor" 
        Height="800" 
        Width="960"
        MinHeight="500"
        MinWidth="600"
        Background="{StaticResource BackgroundBrush}"
        WindowStartupLocation="CenterScreen">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource Card}">
            <StackPanel>
                <TextBlock Text="🔋 Battery Drain Monitor" 
                          FontSize="24" 
                          FontWeight="Bold" 
                          Foreground="{StaticResource TextBrush}"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,8"/>
                <TextBlock x:Name="StatusTextBlock" 
                          Text="Ready to monitor battery drain" 
                          FontSize="14" 
                          Foreground="{StaticResource TextSecondaryBrush}"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Current Battery Status -->
        <Border Grid.Row="1" Style="{StaticResource Card}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="Current Battery" 
                              FontWeight="Medium" 
                              Foreground="{StaticResource TextSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock x:Name="BatteryPercentageTextBlock" 
                              Text="--%" 
                              FontSize="32" 
                              FontWeight="Bold" 
                              Foreground="{StaticResource PrimaryBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock x:Name="PowerStatusTextBlock" 
                              Text="Unknown" 
                              FontSize="12" 
                              Foreground="{StaticResource TextSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="Drain Rate" 
                              FontWeight="Medium" 
                              Foreground="{StaticResource TextSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock x:Name="DrainRateTextBlock" 
                              Text="--% /hr" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="{StaticResource WarningBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock x:Name="EstimatedTimeTextBlock" 
                              Text="Time remaining: --" 
                              FontSize="12" 
                              Foreground="{StaticResource TextSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="Current Flow"
                              FontWeight="Medium"
                              Foreground="{StaticResource TextSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock x:Name="CurrentFlowTextBlock"
                              Text="-- mA"
                              FontSize="20"
                              FontWeight="Bold"
                              Foreground="{StaticResource PrimaryBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock x:Name="CurrentFlowStatusTextBlock"
                              Text="Unknown"
                              FontSize="12"
                              Foreground="{StaticResource TextSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="Battery Health"
                              FontWeight="Medium"
                              Foreground="{StaticResource TextSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock x:Name="BatteryHealthTextBlock"
                              Text="--%"
                              FontSize="20"
                              FontWeight="Bold"
                              Foreground="{StaticResource SuccessBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock x:Name="PowerFlowTextBlock"
                              Text="-- W"
                              FontSize="12"
                              Foreground="{StaticResource TextSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Controls -->
        <Border Grid.Row="2" Style="{StaticResource Card}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="Log Interval:" 
                              VerticalAlignment="Center" 
                              Margin="0,0,8,0"
                              Foreground="{StaticResource TextBrush}"/>
                    <ComboBox x:Name="IntervalComboBox" 
                             Width="100" 
                             SelectedIndex="2">
                        <ComboBoxItem Content="10 sec" Tag="10"/>
                        <ComboBoxItem Content="30 sec" Tag="30"/>
                        <ComboBoxItem Content="1 min" Tag="60"/>
                        <ComboBoxItem Content="5 min" Tag="300"/>
                        <ComboBoxItem Content="10 min" Tag="600"/>
                        <ComboBoxItem Content="30 min" Tag="1800"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="StartStopButton"
                           Content="Start Monitoring"
                           Style="{StaticResource SophisticatedButton}"
                           Click="StartStopButton_Click"/>
                    <CheckBox x:Name="PreventSleepCheckBox"
                             Content="Prevent Sleep"
                             VerticalAlignment="Center"
                             Margin="16,0,0,0"
                             Style="{StaticResource SophisticatedCheckBox}"
                             Checked="PreventSleepCheckBox_Checked"
                             Unchecked="PreventSleepCheckBox_Unchecked"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="OpenLogButton"
                           Content="Open Log File"
                           Style="{StaticResource SophisticatedSecondaryButton}"
                           Click="OpenLogButton_Click"/>
                    <Button x:Name="ClearLogButton"
                           Content="Clear Log"
                           Style="{StaticResource SophisticatedDangerButton}"
                           Click="ClearLogButton_Click"
                           Margin="8,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Statistics and Log -->
        <Border Grid.Row="3" Style="{StaticResource Card}">
            <TabControl>
                <TabItem Header="Recent Statistics">
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Button Content="Last Hour" Click="ShowStats_Click" Tag="1" Style="{StaticResource SophisticatedButton}"/>
                            <Button Content="Last 6 Hours" Click="ShowStats_Click" Tag="6" Style="{StaticResource SophisticatedButton}"/>
                            <Button Content="Last 24 Hours" Click="ShowStats_Click" Tag="24" Style="{StaticResource SophisticatedButton}"/>
                        </StackPanel>
                        
                        <TextBlock x:Name="StatsTextBlock" 
                                  Grid.Row="1"
                                  Text="Select a time period to view battery drain statistics"
                                  FontSize="14"
                                  Foreground="{StaticResource TextBrush}"
                                  TextWrapping="Wrap"
                                  VerticalAlignment="Top"/>
                    </Grid>
                </TabItem>
                
                <TabItem Header="Log Entries">
                    <DataGrid x:Name="LogDataGrid" 
                             AutoGenerateColumns="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             Margin="8">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Time" Binding="{Binding Timestamp, StringFormat='{}{0:HH:mm:ss}'}" Width="70"/>
                            <DataGridTextColumn Header="Date" Binding="{Binding Timestamp, StringFormat='{}{0:yyyy-MM-dd}'}" Width="90"/>
                            <DataGridTextColumn Header="Battery %" Binding="{Binding BatteryPercentage}" Width="70"/>
                            <DataGridTextColumn Header="Power Status" Binding="{Binding PowerStatus}" Width="90"/>
                            <DataGridTextColumn Header="Current (mA)" Binding="{Binding CurrentFlow}" Width="90"/>
                            <DataGridTextColumn Header="Power (W)" Binding="{Binding PowerFlow, StringFormat='{}{0:F1}'}" Width="80"/>
                            <DataGridTextColumn Header="Drain Rate (%/hr)" Binding="{Binding DrainRate, StringFormat='{}{0:F2}'}" Width="110"/>
                            <DataGridTextColumn Header="Est. Time (min)" Binding="{Binding EstimatedTimeRemaining}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </TabItem>
            </TabControl>
        </Border>
        
        <!-- Footer -->
        <Border Grid.Row="4" Style="{StaticResource Card}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock x:Name="LogFilePathTextBlock" 
                          Grid.Column="0"
                          Text="Log file: Not set" 
                          FontSize="12" 
                          Foreground="{StaticResource TextSecondaryBrush}"
                          VerticalAlignment="Center"/>
                
                <TextBlock x:Name="LastUpdateTextBlock" 
                          Grid.Column="1"
                          Text="Last update: Never" 
                          FontSize="12" 
                          Foreground="{StaticResource TextSecondaryBrush}"
                          VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
