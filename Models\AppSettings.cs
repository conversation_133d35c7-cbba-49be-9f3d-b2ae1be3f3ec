using System.IO;

namespace BatteryDrainMonitor.Models;

public class AppSettings
{
    public int DefaultLogIntervalSeconds { get; set; } = 60;
    public bool PreventSleepOnStartup { get; set; } = false;
    public bool StartMonitoringOnStartup { get; set; } = false;
    public string LogDirectory { get; set; } = string.Empty;
    public bool MinimizeToTray { get; set; } = false;
    public bool ShowNotifications { get; set; } = true;
    public int LowBatteryWarningThreshold { get; set; } = 20;
    public int CriticalBatteryWarningThreshold { get; set; } = 10;
}

public static class AppConfig
{
    private static AppSettings? _settings;
    
    public static AppSettings Settings
    {
        get
        {
            if (_settings == null)
            {
                LoadSettings();
            }
            return _settings!;
        }
    }
    
    private static void LoadSettings()
    {
        // For now, use default settings
        // In a full implementation, this would load from a config file
        _settings = new AppSettings();
        
        // Set default log directory
        _settings.LogDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
            "BatteryDrainMonitor"
        );
    }
    
    public static void SaveSettings()
    {
        // In a full implementation, this would save to a config file
        // For now, settings are not persisted
    }
}
