using System.Runtime.InteropServices;

namespace BatteryDrainMonitor.Services;

public class SleepPreventionService
{
    [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    private static extern uint SetThreadExecutionState(uint esFlags);

    private const uint ES_CONTINUOUS = 0x80000000;
    private const uint ES_SYSTEM_REQUIRED = 0x00000001;
    private const uint ES_DISPLAY_REQUIRED = 0x00000002;

    private bool _isPreventingSleep = false;

    public bool IsPreventingSleep => _isPreventingSleep;

    public void PreventSleep()
    {
        if (!_isPreventingSleep)
        {
            // Prevent system sleep and display sleep
            var result = SetThreadExecutionState(ES_CONTINUOUS | ES_SYSTEM_REQUIRED | ES_DISPLAY_REQUIRED);
            _isPreventingSleep = result != 0;
        }
    }

    public void AllowSleep()
    {
        if (_isPreventingSleep)
        {
            // Reset to normal power management
            var result = SetThreadExecutionState(ES_CONTINUOUS);
            _isPreventingSleep = false;
        }
    }

    public void Dispose()
    {
        AllowSleep();
    }
}
