<Application x:Class="BatteryDrainMonitor.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <!-- Modern color scheme -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="TextBrush" Color="#212121"/>
            <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
            
            <!-- Sophisticated Button Styles -->

            <!-- Primary Sophisticated Button -->
            <Style x:Key="SophisticatedButton" TargetType="Button">
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="20,10"/>
                <Setter Property="Margin" Value="6"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="FontSize" Value="13"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <!-- Shadow Layer -->
                                <Border x:Name="ShadowBorder"
                                        Background="#40000000"
                                        CornerRadius="8"
                                        Margin="0,2,0,0"
                                        Opacity="0.3"/>

                                <!-- Main Button -->
                                <Border x:Name="MainBorder"
                                        CornerRadius="6"
                                        BorderThickness="1"
                                        BorderBrush="#20FFFFFF">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#2196F3" Offset="0"/>
                                            <GradientStop Color="#1976D2" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>

                                    <!-- Inner Highlight -->
                                    <Border CornerRadius="5" Margin="1">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,0.3">
                                                <GradientStop Color="#40FFFFFF" Offset="0"/>
                                                <GradientStop Color="Transparent" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.Background>

                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </Border>
                            </Grid>

                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="MainBorder" Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="#42A5F5" Offset="0"/>
                                                <GradientStop Color="#1E88E5" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.5"/>
                                </Trigger>

                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="MainBorder" Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="#1565C0" Offset="0"/>
                                                <GradientStop Color="#0D47A1" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="MainBorder" Property="RenderTransform">
                                        <Setter.Value>
                                            <TranslateTransform Y="1"/>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.2"/>
                                </Trigger>

                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter TargetName="MainBorder" Property="Background" Value="#BDBDBD"/>
                                    <Setter Property="Foreground" Value="#757575"/>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.1"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Secondary Sophisticated Button (Amber/Warning) -->
            <Style x:Key="SophisticatedSecondaryButton" TargetType="Button" BasedOn="{StaticResource SophisticatedButton}">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <!-- Shadow Layer -->
                                <Border x:Name="ShadowBorder"
                                        Background="#40000000"
                                        CornerRadius="8"
                                        Margin="0,2,0,0"
                                        Opacity="0.3"/>

                                <!-- Main Button -->
                                <Border x:Name="MainBorder"
                                        CornerRadius="6"
                                        BorderThickness="1"
                                        BorderBrush="#20FFFFFF">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#FFC107" Offset="0"/>
                                            <GradientStop Color="#FF8F00" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>

                                    <!-- Inner Highlight -->
                                    <Border CornerRadius="5" Margin="1">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,0.3">
                                                <GradientStop Color="#40FFFFFF" Offset="0"/>
                                                <GradientStop Color="Transparent" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.Background>

                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </Border>
                            </Grid>

                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="MainBorder" Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="#FFD54F" Offset="0"/>
                                                <GradientStop Color="#FFA000" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.5"/>
                                </Trigger>

                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="MainBorder" Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="#FF8F00" Offset="0"/>
                                                <GradientStop Color="#E65100" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="MainBorder" Property="RenderTransform">
                                        <Setter.Value>
                                            <TranslateTransform Y="1"/>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.2"/>
                                </Trigger>

                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter TargetName="MainBorder" Property="Background" Value="#BDBDBD"/>
                                    <Setter Property="Foreground" Value="#757575"/>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.1"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Danger Sophisticated Button (Red) -->
            <Style x:Key="SophisticatedDangerButton" TargetType="Button" BasedOn="{StaticResource SophisticatedButton}">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <!-- Shadow Layer -->
                                <Border x:Name="ShadowBorder"
                                        Background="#40000000"
                                        CornerRadius="8"
                                        Margin="0,2,0,0"
                                        Opacity="0.3"/>

                                <!-- Main Button -->
                                <Border x:Name="MainBorder"
                                        CornerRadius="6"
                                        BorderThickness="1"
                                        BorderBrush="#20FFFFFF">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#F44336" Offset="0"/>
                                            <GradientStop Color="#D32F2F" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>

                                    <!-- Inner Highlight -->
                                    <Border CornerRadius="5" Margin="1">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,0.3">
                                                <GradientStop Color="#40FFFFFF" Offset="0"/>
                                                <GradientStop Color="Transparent" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.Background>

                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </Border>
                            </Grid>

                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="MainBorder" Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="#EF5350" Offset="0"/>
                                                <GradientStop Color="#E53935" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.5"/>
                                </Trigger>

                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="MainBorder" Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="#C62828" Offset="0"/>
                                                <GradientStop Color="#B71C1C" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="MainBorder" Property="RenderTransform">
                                        <Setter.Value>
                                            <TranslateTransform Y="1"/>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.2"/>
                                </Trigger>

                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter TargetName="MainBorder" Property="Background" Value="#BDBDBD"/>
                                    <Setter Property="Foreground" Value="#757575"/>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.1"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Success Sophisticated Button (Green) -->
            <Style x:Key="SophisticatedSuccessButton" TargetType="Button" BasedOn="{StaticResource SophisticatedButton}">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <!-- Shadow Layer -->
                                <Border x:Name="ShadowBorder"
                                        Background="#40000000"
                                        CornerRadius="8"
                                        Margin="0,2,0,0"
                                        Opacity="0.3"/>

                                <!-- Main Button -->
                                <Border x:Name="MainBorder"
                                        CornerRadius="6"
                                        BorderThickness="1"
                                        BorderBrush="#20FFFFFF">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#4CAF50" Offset="0"/>
                                            <GradientStop Color="#388E3C" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>

                                    <!-- Inner Highlight -->
                                    <Border CornerRadius="5" Margin="1">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,0.3">
                                                <GradientStop Color="#40FFFFFF" Offset="0"/>
                                                <GradientStop Color="Transparent" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.Background>

                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </Border>
                            </Grid>

                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="MainBorder" Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="#66BB6A" Offset="0"/>
                                                <GradientStop Color="#43A047" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.5"/>
                                </Trigger>

                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="MainBorder" Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="#2E7D32" Offset="0"/>
                                                <GradientStop Color="#1B5E20" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="MainBorder" Property="RenderTransform">
                                        <Setter.Value>
                                            <TranslateTransform Y="1"/>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.2"/>
                                </Trigger>

                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter TargetName="MainBorder" Property="Background" Value="#BDBDBD"/>
                                    <Setter Property="Foreground" Value="#757575"/>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.1"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Sophisticated CheckBox Style -->
            <Style x:Key="SophisticatedCheckBox" TargetType="CheckBox">
                <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="FontSize" Value="13"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="CheckBox">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Custom CheckBox -->
                                <Border x:Name="CheckBoxBorder"
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        CornerRadius="4"
                                        BorderThickness="2"
                                        BorderBrush="{StaticResource PrimaryBrush}"
                                        Background="White"
                                        Margin="0,0,8,0">

                                    <!-- Check Mark -->
                                    <Path x:Name="CheckMark"
                                          Data="M 3,8 L 7,12 L 15,4"
                                          Stroke="White"
                                          StrokeThickness="2.5"
                                          Visibility="Collapsed"/>
                                </Border>

                                <!-- Content -->
                                <ContentPresenter Grid.Column="1"
                                                VerticalAlignment="Center"
                                                Content="{TemplateBinding Content}"/>
                            </Grid>

                            <ControlTemplate.Triggers>
                                <Trigger Property="IsChecked" Value="True">
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="{StaticResource PrimaryBrush}"/>
                                    <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                                </Trigger>

                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="#42A5F5"/>
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="#F3F9FF"/>
                                </Trigger>

                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsMouseOver" Value="True"/>
                                        <Condition Property="IsChecked" Value="True"/>
                                    </MultiTrigger.Conditions>
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="#42A5F5"/>
                                </MultiTrigger>

                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="#1976D2"/>
                                </Trigger>

                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="#BDBDBD"/>
                                    <Setter TargetName="CheckBoxBorder" Property="Background" Value="#F5F5F5"/>
                                    <Setter Property="Foreground" Value="#BDBDBD"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Card style -->
            <Style x:Key="Card" TargetType="Border">
                <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="CornerRadius" Value="8"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
