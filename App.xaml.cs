using System.Windows;

namespace BatteryDrainMonitor;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);
        
        // Set up global exception handling
        DispatcherUnhandledException += (sender, args) =>
        {
            MessageBox.Show($"An error occurred: {args.Exception.Message}", 
                          "Battery Drain Monitor Error", 
                          MessageBoxButton.OK, 
                          MessageBoxImage.Error);
            args.Handled = true;
        };
    }
}
