using System.Diagnostics;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;
using BatteryDrainMonitor.Services;

namespace BatteryDrainMonitor;

public partial class MainWindow : Window
{
    private readonly BatteryService _batteryService;
    private readonly SleepPreventionService _sleepPreventionService;
    private readonly LoggingService _loggingService;
    private readonly DispatcherTimer _updateTimer;
    private bool _isMonitoring = false;

    public MainWindow()
    {
        InitializeComponent();
        
        _batteryService = new BatteryService();
        _sleepPreventionService = new SleepPreventionService();
        _loggingService = new LoggingService();
        
        _updateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _updateTimer.Tick += UpdateTimer_Tick;
        
        InitializeUI();
        UpdateBatteryDisplay();
        
        // Start the update timer for real-time display
        _updateTimer.Start();
    }

    private void InitializeUI()
    {
        // Check if battery is present
        if (!_batteryService.IsBatteryPresent())
        {
            StatusTextBlock.Text = "⚠️ No battery detected - Running on AC power";
            StatusTextBlock.Foreground = new SolidColorBrush(Colors.Orange);
        }
        
        // Set log file path
        LogFilePathTextBlock.Text = $"Log file: {_loggingService.LogFilePath}";
        
        // Load existing log entries
        RefreshLogDisplay();
        
        // Get battery health
        BatteryHealthTextBlock.Text = _batteryService.GetBatteryHealth();
    }

    private void UpdateTimer_Tick(object? sender, EventArgs e)
    {
        UpdateBatteryDisplay();
        LastUpdateTextBlock.Text = $"Last update: {DateTime.Now:HH:mm:ss}";
    }

    private void UpdateBatteryDisplay()
    {
        var batteryInfo = _batteryService.GetCurrentBatteryInfo();
        
        // Update battery percentage
        BatteryPercentageTextBlock.Text = $"{batteryInfo.Percentage}%";
        
        // Update power status
        PowerStatusTextBlock.Text = batteryInfo.PowerStatus.ToString();
        
        // Update estimated time
        if (batteryInfo.EstimatedTime > 0)
        {
            var hours = batteryInfo.EstimatedTime / 60;
            var minutes = batteryInfo.EstimatedTime % 60;
            EstimatedTimeTextBlock.Text = $"Time remaining: {hours}h {minutes}m";
        }
        else
        {
            EstimatedTimeTextBlock.Text = "Time remaining: Unknown";
        }

        // Update current flow information
        UpdateCurrentFlowDisplay(batteryInfo);
        
        // Update battery percentage color based on level
        if (batteryInfo.Percentage <= 20)
        {
            BatteryPercentageTextBlock.Foreground = FindResource("DangerBrush") as SolidColorBrush;
        }
        else if (batteryInfo.Percentage <= 50)
        {
            BatteryPercentageTextBlock.Foreground = FindResource("WarningBrush") as SolidColorBrush;
        }
        else
        {
            BatteryPercentageTextBlock.Foreground = FindResource("SuccessBrush") as SolidColorBrush;
        }
        
        // Update drain rate if we have recent log entries
        var recentStats = _loggingService.GetDrainStats(TimeSpan.FromHours(1));
        if (recentStats.TimeElapsed.TotalMinutes > 5)
        {
            DrainRateTextBlock.Text = $"{recentStats.AverageDrainRate:F1}% /hr";
            
            // Color code drain rate
            if (recentStats.AverageDrainRate > 20)
            {
                DrainRateTextBlock.Foreground = FindResource("DangerBrush") as SolidColorBrush;
            }
            else if (recentStats.AverageDrainRate > 10)
            {
                DrainRateTextBlock.Foreground = FindResource("WarningBrush") as SolidColorBrush;
            }
            else
            {
                DrainRateTextBlock.Foreground = FindResource("SuccessBrush") as SolidColorBrush;
            }
        }
    }

    private void UpdateCurrentFlowDisplay(BatteryInfo batteryInfo)
    {
        if (batteryInfo.IsCurrentDataAvailable)
        {
            // Display current flow
            CurrentFlowTextBlock.Text = $"{batteryInfo.CurrentFlow:N0} mA";

            // Update power flow
            PowerFlowTextBlock.Text = $"{batteryInfo.PowerFlow:F1} W";

            // Color code and set status based on current flow
            if (batteryInfo.CurrentFlow > 0)
            {
                // Charging
                CurrentFlowTextBlock.Foreground = FindResource("SuccessBrush") as SolidColorBrush;
                CurrentFlowStatusTextBlock.Text = "⚡ Charging";
                CurrentFlowStatusTextBlock.Foreground = FindResource("SuccessBrush") as SolidColorBrush;
            }
            else if (batteryInfo.CurrentFlow < 0)
            {
                // Discharging
                var absCurrentFlow = Math.Abs(batteryInfo.CurrentFlow);
                CurrentFlowTextBlock.Text = $"{absCurrentFlow:N0} mA";

                if (absCurrentFlow > 2000)
                {
                    CurrentFlowTextBlock.Foreground = FindResource("DangerBrush") as SolidColorBrush;
                    CurrentFlowStatusTextBlock.Text = "🔥 High drain";
                    CurrentFlowStatusTextBlock.Foreground = FindResource("DangerBrush") as SolidColorBrush;
                }
                else if (absCurrentFlow > 1000)
                {
                    CurrentFlowTextBlock.Foreground = FindResource("WarningBrush") as SolidColorBrush;
                    CurrentFlowStatusTextBlock.Text = "⚠️ Moderate drain";
                    CurrentFlowStatusTextBlock.Foreground = FindResource("WarningBrush") as SolidColorBrush;
                }
                else
                {
                    CurrentFlowTextBlock.Foreground = FindResource("SuccessBrush") as SolidColorBrush;
                    CurrentFlowStatusTextBlock.Text = "✅ Low drain";
                    CurrentFlowStatusTextBlock.Foreground = FindResource("SuccessBrush") as SolidColorBrush;
                }
            }
            else
            {
                // No current flow (rare)
                CurrentFlowTextBlock.Foreground = FindResource("TextSecondaryBrush") as SolidColorBrush;
                CurrentFlowStatusTextBlock.Text = "⏸️ No flow";
                CurrentFlowStatusTextBlock.Foreground = FindResource("TextSecondaryBrush") as SolidColorBrush;
            }
        }
        else
        {
            // Current data not available
            CurrentFlowTextBlock.Text = "-- mA";
            CurrentFlowTextBlock.Foreground = FindResource("TextSecondaryBrush") as SolidColorBrush;
            CurrentFlowStatusTextBlock.Text = "Data unavailable";
            CurrentFlowStatusTextBlock.Foreground = FindResource("TextSecondaryBrush") as SolidColorBrush;
            PowerFlowTextBlock.Text = "-- W";
        }
    }

    private void StartStopButton_Click(object sender, RoutedEventArgs e)
    {
        if (_isMonitoring)
        {
            StopMonitoring();
        }
        else
        {
            StartMonitoring();
        }
    }

    private void StartMonitoring()
    {
        var selectedItem = IntervalComboBox.SelectedItem as ComboBoxItem;
        if (selectedItem?.Tag is string tagValue && int.TryParse(tagValue, out int intervalSeconds))
        {
            _isMonitoring = true;
            StartStopButton.Content = "Stop Monitoring";
            StartStopButton.Background = FindResource("DangerBrush") as SolidColorBrush;
            StatusTextBlock.Text = $"🔄 Monitoring battery every {selectedItem.Content}";
            StatusTextBlock.Foreground = FindResource("SuccessBrush") as SolidColorBrush;
            
            // Start logging
            _batteryService.StartMonitoring(intervalSeconds);
            _batteryService.BatteryStatusChanged += OnBatteryStatusChanged;
            
            // Log initial status
            var initialInfo = _batteryService.GetCurrentBatteryInfo();
            _loggingService.LogBatteryStatus(initialInfo);
            RefreshLogDisplay();
        }
    }

    private void StopMonitoring()
    {
        _isMonitoring = false;
        StartStopButton.Content = "Start Monitoring";
        StartStopButton.Background = FindResource("PrimaryBrush") as SolidColorBrush;
        StatusTextBlock.Text = "⏸️ Monitoring stopped";
        StatusTextBlock.Foreground = FindResource("TextSecondaryBrush") as SolidColorBrush;
        
        _batteryService.BatteryStatusChanged -= OnBatteryStatusChanged;
    }

    private void OnBatteryStatusChanged(object? sender, BatteryInfo batteryInfo)
    {
        Dispatcher.Invoke(() =>
        {
            _loggingService.LogBatteryStatus(batteryInfo);
            RefreshLogDisplay();
        });
    }

    private void PreventSleepCheckBox_Checked(object sender, RoutedEventArgs e)
    {
        _sleepPreventionService.PreventSleep();
        StatusTextBlock.Text = "🚫 Sleep mode prevented";
    }

    private void PreventSleepCheckBox_Unchecked(object sender, RoutedEventArgs e)
    {
        _sleepPreventionService.AllowSleep();
        StatusTextBlock.Text = "💤 Sleep mode allowed";
    }

    private void OpenLogButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (File.Exists(_loggingService.LogFilePath))
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = _loggingService.LogFilePath,
                    UseShellExecute = true
                });
            }
            else
            {
                MessageBox.Show("Log file does not exist yet. Start monitoring to create it.", 
                              "Log File Not Found", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error opening log file: {ex.Message}", 
                          "Error", 
                          MessageBoxButton.OK, 
                          MessageBoxImage.Error);
        }
    }

    private void ClearLogButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("Are you sure you want to clear the log file? This action cannot be undone.", 
                                   "Confirm Clear Log", 
                                   MessageBoxButton.YesNo, 
                                   MessageBoxImage.Warning);
        
        if (result == MessageBoxResult.Yes)
        {
            try
            {
                if (File.Exists(_loggingService.LogFilePath))
                {
                    File.Delete(_loggingService.LogFilePath);
                }
                RefreshLogDisplay();
                MessageBox.Show("Log file cleared successfully.", 
                              "Log Cleared", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error clearing log file: {ex.Message}", 
                              "Error", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Error);
            }
        }
    }

    private void ShowStats_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string hoursStr && int.TryParse(hoursStr, out int hours))
        {
            var stats = _loggingService.GetDrainStats(TimeSpan.FromHours(hours));
            
            if (stats.TimeElapsed.TotalMinutes < 1)
            {
                StatsTextBlock.Text = $"No data available for the last {hours} hour(s). Start monitoring to collect data.";
            }
            else
            {
                StatsTextBlock.Text = $"📊 Battery Statistics (Last {hours} hour(s)):\n\n" +
                                    $"• Time Period: {stats.TimeElapsed.Hours}h {stats.TimeElapsed.Minutes}m\n" +
                                    $"• Battery Level: {stats.StartPercentage}% → {stats.EndPercentage}%\n" +
                                    $"• Total Drain: {stats.TotalDrain}%\n" +
                                    $"• Average Drain Rate: {stats.AverageDrainRate:F2}% per hour\n" +
                                    $"• Estimated Time to Empty: {(stats.EndPercentage / Math.Max(stats.AverageDrainRate, 0.1)):F1} hours\n\n" +
                                    GetDrainRateAssessment(stats.AverageDrainRate);
            }
        }
    }

    private string GetDrainRateAssessment(double drainRate)
    {
        return drainRate switch
        {
            < 5 => "✅ Excellent - Very low power consumption",
            < 10 => "✅ Good - Normal power consumption",
            < 15 => "⚠️ Moderate - Consider closing unnecessary applications",
            < 25 => "⚠️ High - Check for power-hungry applications",
            _ => "🚨 Very High - Investigate background processes and hardware issues"
        };
    }

    private void RefreshLogDisplay()
    {
        LogDataGrid.ItemsSource = _loggingService.LogEntries.OrderByDescending(e => e.Timestamp).ToList();
    }

    protected override void OnClosed(EventArgs e)
    {
        _sleepPreventionService.Dispose();
        _updateTimer.Stop();
        base.OnClosed(e);
    }
}
