using System.IO;
using System.Text;

namespace BatteryDrainMonitor.Services;

public class LogEntry
{
    public DateTime Timestamp { get; set; }
    public int BatteryPercentage { get; set; }
    public string PowerStatus { get; set; } = string.Empty;
    public int EstimatedTimeRemaining { get; set; }
    public double DrainRate { get; set; } // Percentage per hour
    public int CurrentFlow { get; set; } // in mA (positive = charging, negative = discharging)
    public int Voltage { get; set; } // in mV
    public double PowerFlow { get; set; } // in watts
    public bool IsCurrentDataAvailable { get; set; }
}

public class LoggingService
{
    private readonly string _logFilePath;
    private readonly List<LogEntry> _logEntries = new();
    private LogEntry? _previousEntry;

    public LoggingService(string? logFilePath = null)
    {
        _logFilePath = logFilePath ?? Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
            "BatteryDrainMonitor",
            $"battery_log_{DateTime.Now:yyyy-MM-dd}.csv"
        );
        
        // Ensure directory exists
        var directory = Path.GetDirectoryName(_logFilePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }
        
        // Load existing log entries
        LoadExistingLog();
    }

    public string LogFilePath => _logFilePath;
    public IReadOnlyList<LogEntry> LogEntries => _logEntries.AsReadOnly();

    public void LogBatteryStatus(BatteryInfo batteryInfo)
    {
        var drainRate = CalculateDrainRate(batteryInfo);
        
        var entry = new LogEntry
        {
            Timestamp = batteryInfo.Timestamp,
            BatteryPercentage = batteryInfo.Percentage,
            PowerStatus = batteryInfo.PowerStatus.ToString(),
            EstimatedTimeRemaining = batteryInfo.EstimatedTime,
            DrainRate = drainRate,
            CurrentFlow = batteryInfo.CurrentFlow,
            Voltage = batteryInfo.Voltage,
            PowerFlow = batteryInfo.PowerFlow,
            IsCurrentDataAvailable = batteryInfo.IsCurrentDataAvailable
        };

        _logEntries.Add(entry);
        _previousEntry = entry;
        
        // Write to file
        WriteLogEntry(entry);
    }

    private double CalculateDrainRate(BatteryInfo currentInfo)
    {
        if (_previousEntry == null)
            return 0;

        var timeDiff = currentInfo.Timestamp - _previousEntry.Timestamp;
        if (timeDiff.TotalMinutes < 1)
            return _previousEntry.DrainRate; // Return previous rate if too little time has passed

        var percentageDiff = _previousEntry.BatteryPercentage - currentInfo.Percentage;
        var hoursElapsed = timeDiff.TotalHours;
        
        return hoursElapsed > 0 ? percentageDiff / hoursElapsed : 0;
    }

    private void LoadExistingLog()
    {
        if (!File.Exists(_logFilePath))
            return;

        try
        {
            var lines = File.ReadAllLines(_logFilePath);
            if (lines.Length <= 1) // Skip header or empty file
                return;

            for (int i = 1; i < lines.Length; i++) // Skip header
            {
                var parts = lines[i].Split(',');
                if (parts.Length >= 5)
                {
                    var entry = new LogEntry
                    {
                        Timestamp = DateTime.Parse(parts[0]),
                        BatteryPercentage = int.Parse(parts[1]),
                        PowerStatus = parts[2],
                        EstimatedTimeRemaining = int.Parse(parts[3]),
                        DrainRate = double.Parse(parts[4])
                    };

                    // Handle new fields if available (backward compatibility)
                    if (parts.Length >= 9)
                    {
                        entry.CurrentFlow = int.Parse(parts[5]);
                        entry.Voltage = int.Parse(parts[6]);
                        entry.PowerFlow = double.Parse(parts[7]);
                        entry.IsCurrentDataAvailable = bool.Parse(parts[8]);
                    }

                    _logEntries.Add(entry);
                    _previousEntry = entry;
                }
            }
        }
        catch
        {
            // If there's an error reading the log, start fresh
            _logEntries.Clear();
            _previousEntry = null;
        }
    }

    private void WriteLogEntry(LogEntry entry)
    {
        try
        {
            var fileExists = File.Exists(_logFilePath);
            using var writer = new StreamWriter(_logFilePath, append: true, Encoding.UTF8);
            
            // Write header if file is new
            if (!fileExists)
            {
                writer.WriteLine("Timestamp,Battery%,PowerStatus,EstimatedTime(min),DrainRate(%/hr),Current(mA),Voltage(mV),Power(W),CurrentDataAvailable");
            }

            writer.WriteLine($"{entry.Timestamp:yyyy-MM-dd HH:mm:ss},{entry.BatteryPercentage},{entry.PowerStatus},{entry.EstimatedTimeRemaining},{entry.DrainRate:F2},{entry.CurrentFlow},{entry.Voltage},{entry.PowerFlow:F2},{entry.IsCurrentDataAvailable}");
        }
        catch (Exception ex)
        {
            // Log error to event log or show message
            System.Diagnostics.Debug.WriteLine($"Error writing to log file: {ex.Message}");
        }
    }

    public BatteryDrainStats GetDrainStats(TimeSpan period)
    {
        var cutoffTime = DateTime.Now - period;
        var recentEntries = _logEntries.Where(e => e.Timestamp >= cutoffTime).ToList();
        
        if (recentEntries.Count < 2)
            return new BatteryDrainStats();

        var firstEntry = recentEntries.First();
        var lastEntry = recentEntries.Last();
        
        var totalDrain = firstEntry.BatteryPercentage - lastEntry.BatteryPercentage;
        var timeElapsed = lastEntry.Timestamp - firstEntry.Timestamp;
        var averageDrainRate = timeElapsed.TotalHours > 0 ? totalDrain / timeElapsed.TotalHours : 0;

        return new BatteryDrainStats
        {
            TotalDrain = totalDrain,
            TimeElapsed = timeElapsed,
            AverageDrainRate = averageDrainRate,
            StartPercentage = firstEntry.BatteryPercentage,
            EndPercentage = lastEntry.BatteryPercentage
        };
    }
}

public class BatteryDrainStats
{
    public int TotalDrain { get; set; }
    public TimeSpan TimeElapsed { get; set; }
    public double AverageDrainRate { get; set; }
    public int StartPercentage { get; set; }
    public int EndPercentage { get; set; }
}
