# Battery Drain Monitor

A modern WPF application built with C# .NET 9 that monitors your laptop's battery drain and prevents sleep mode while running.

## Features

### 🔋 Core Functionality
- **Real-time Battery Monitoring**: Displays current battery percentage, power status, and estimated time remaining
- **Sleep Prevention**: Prevents your PC from entering sleep mode while the application is running
- **Configurable Logging**: Log battery status at user-defined intervals (10 seconds to 30 minutes)
- **Automatic Log Files**: Creates CSV log files with timestamps, battery percentage, power status, and drain rates

### 📊 Analytics & Statistics
- **Drain Rate Calculation**: Shows battery drain rate in percentage per hour
- **Historical Statistics**: View battery drain statistics for the last 1, 6, or 24 hours
- **Battery Health**: Displays battery health as a percentage of design capacity
- **Smart Assessments**: Provides recommendations based on drain rate patterns

### 🎨 User Interface
- **Modern Design**: Clean, card-based UI with intuitive controls
- **Color-coded Status**: Battery percentage and drain rates are color-coded for quick assessment
- **Tabbed Interface**: Separate tabs for statistics and detailed log entries
- **Real-time Updates**: Live updates every second for current status

## How to Use

### Getting Started
1. **Build and Run**: 
   ```bash
   dotnet build BatteryDrainMonitor.csproj
   dotnet run --project BatteryDrainMonitor.csproj
   ```

2. **Configure Logging Interval**: 
   - Select your preferred logging interval from the dropdown (10 sec to 30 min)
   - Default is 1 minute

3. **Start Monitoring**: 
   - Click "Start Monitoring" to begin logging battery data
   - The app will create a CSV log file in your Documents folder

### Key Features

#### Sleep Prevention
- Check "Prevent Sleep" to keep your PC awake while monitoring
- Useful for long-term battery drain testing
- Automatically releases sleep prevention when unchecked

#### Log Management
- **Open Log File**: View the CSV log in your default spreadsheet application
- **Clear Log**: Remove all logged data (with confirmation)
- **Auto-generated Files**: Logs are saved to `Documents/BatteryDrainMonitor/battery_log_YYYY-MM-DD.csv`

#### Statistics Analysis
- **Recent Statistics Tab**: View drain analysis for different time periods
- **Log Entries Tab**: Browse all recorded battery measurements
- **Drain Rate Assessment**: Get recommendations based on your battery usage patterns

### Understanding the Data

#### Battery Status Colors
- 🟢 **Green (>50%)**: Good battery level
- 🟡 **Yellow (21-50%)**: Moderate battery level  
- 🔴 **Red (≤20%)**: Low battery level

#### Drain Rate Assessment
- **<5% /hr**: ✅ Excellent - Very low power consumption
- **5-10% /hr**: ✅ Good - Normal power consumption
- **10-15% /hr**: ⚠️ Moderate - Consider closing unnecessary applications
- **15-25% /hr**: ⚠️ High - Check for power-hungry applications
- **>25% /hr**: 🚨 Very High - Investigate background processes

## Technical Details

### Requirements
- Windows 10/11
- .NET 9.0 Runtime
- WPF support

### Dependencies
- `System.Management` - For advanced battery information
- `Microsoft.WindowsDesktop.App` - For WPF and Windows Forms integration

### Log File Format
The CSV log files contain the following columns:
- **Timestamp**: Date and time of measurement
- **Battery%**: Battery percentage at time of measurement
- **PowerStatus**: AC power connection status
- **EstimatedTime(min)**: System-estimated time remaining
- **DrainRate(%/hr)**: Calculated drain rate in percentage per hour

### Architecture
- **Services Layer**: Separate services for battery monitoring, sleep prevention, and logging
- **MVVM Pattern**: Clean separation between UI and business logic
- **Real-time Updates**: Timer-based updates for responsive UI
- **Error Handling**: Comprehensive error handling with user-friendly messages

## Troubleshooting

### Common Issues
1. **"No battery detected"**: Normal when running on a desktop PC or when battery is not properly detected
2. **Log file access errors**: Ensure the application has write permissions to the Documents folder
3. **Sleep prevention not working**: Try running the application as administrator

### Performance Notes
- The application uses minimal system resources
- Logging intervals of 1 minute or longer are recommended for normal use
- Shorter intervals (10-30 seconds) are useful for detailed analysis but generate larger log files

## Future Enhancements
- System tray integration
- Battery health trend analysis
- Export statistics to different formats
- Customizable alert thresholds
- Power profile recommendations

---

**Built with ❤️ using C# .NET 9 and WPF**
